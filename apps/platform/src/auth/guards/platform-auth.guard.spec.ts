import { ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { ApiKeyAuthStrategy, BearerTokenHttpStrategy, User } from '@repo/nestjs-commons/guards';
import { FastifyRequest } from 'fastify';
import { PrivilegedAppAuthStrategy } from '../strategies/privileged-app-auth.strategy';
import { PlatformAuthGuard } from './platform-auth.guard';

describe('PlatformAuthGuard - Privileged App Flag Propagation', () => {
  let guard: PlatformAuthGuard;
  let privilegedAppStrategy: jest.Mocked<PrivilegedAppAuthStrategy>;
  let apiKeyStrategy: jest.Mocked<ApiKeyAuthStrategy>;
  let bearerTokenStrategy: jest.Mocked<BearerTokenHttpStrategy>;
  let reflector: jest.Mocked<Reflector>;

  const mockPrivilegedUser: User = {
    authId: 'auth123',
    sub: 'user123',
    uid: 'user-uid-123',
    email: '<EMAIL>',
    userName: 'user',
    userType: 'ORG_ADMIN',
    scopes: ['read', 'write'],
    orgId: 'org123',
    orgUid: 'org-uid-123',
    orgTier: 'PREMIUM',
    metadata: { timezone: 'UTC' },
    token: 'user-token',
    isPrivilegedApp: true, // This should be propagated
  };

  const mockRegularUser: User = {
    authId: 'auth456',
    sub: 'user456',
    uid: 'user-uid-456',
    email: '<EMAIL>',
    userName: 'regular',
    userType: 'ORG_MEMBER',
    scopes: ['read'],
    orgId: 'org456',
    orgUid: 'org-uid-456',
    orgTier: 'FREE',
    metadata: { timezone: 'EST' },
    token: 'regular-token',
    isPrivilegedApp: false, // This should be propagated as false
  };

  const mockUserWithoutFlag: User = {
    authId: 'auth789',
    sub: 'user789',
    uid: 'user-uid-789',
    email: '<EMAIL>',
    userName: 'noflag',
    userType: 'ORG_MEMBER',
    scopes: ['read'],
    orgId: 'org789',
    orgUid: 'org-uid-789',
    orgTier: 'FREE',
    metadata: { timezone: 'PST' },
    token: 'noflag-token',
    // isPrivilegedApp is undefined - should default to false
  };

  beforeEach(async () => {
    const mockPrivilegedAppStrategy = {
      authenticate: jest.fn(),
    };

    const mockApiKeyStrategy = {
      authenticate: jest.fn(),
    };

    const mockBearerTokenStrategy = {
      authenticate: jest.fn(),
    };

    const mockReflector = {
      getAllAndOverride: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlatformAuthGuard,
        {
          provide: PrivilegedAppAuthStrategy,
          useValue: mockPrivilegedAppStrategy,
        },
        {
          provide: ApiKeyAuthStrategy,
          useValue: mockApiKeyStrategy,
        },
        {
          provide: BearerTokenHttpStrategy,
          useValue: mockBearerTokenStrategy,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
      ],
    }).compile();

    guard = module.get<PlatformAuthGuard>(PlatformAuthGuard);
    privilegedAppStrategy = module.get(PrivilegedAppAuthStrategy);
    apiKeyStrategy = module.get(ApiKeyAuthStrategy);
    bearerTokenStrategy = module.get(BearerTokenHttpStrategy);
    reflector = module.get(Reflector);
  });

  describe('Item 6: Privileged App Flag Propagation', () => {
    it('should propagate isPrivilegedApp=true flag to platform user object', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user123',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock privileged app authentication
      jest.spyOn(PrivilegedAppAuthStrategy, 'shouldUse').mockReturnValue(true);
      privilegedAppStrategy.authenticate.mockResolvedValue(mockPrivilegedUser);

      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();
      expect((mockRequest.user as any).isPrivilegedApp).toBe(true);
      expect((mockRequest.user as any).sub).toBe('user123');
      expect((mockRequest.user as any).email).toBe('<EMAIL>');
    });

    it('should propagate isPrivilegedApp=false flag to platform user object', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock regular API key authentication
      jest.spyOn(PrivilegedAppAuthStrategy, 'shouldUse').mockReturnValue(false);
      apiKeyStrategy.authenticate.mockResolvedValue(mockRegularUser);

      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();
      expect((mockRequest.user as any).isPrivilegedApp).toBe(false);
      expect((mockRequest.user as any).sub).toBe('user456');
      expect((mockRequest.user as any).email).toBe('<EMAIL>');
    });

    it('should default isPrivilegedApp to false when flag is undefined', async () => {
      const mockRequest = {
        headers: {
          authorization: 'Bearer test-token',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock bearer token authentication
      jest.spyOn(PrivilegedAppAuthStrategy, 'shouldUse').mockReturnValue(false);
      bearerTokenStrategy.authenticate.mockResolvedValue(mockUserWithoutFlag);

      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();
      expect((mockRequest.user as any).isPrivilegedApp).toBe(false);
      expect((mockRequest.user as any).sub).toBe('user789');
      expect((mockRequest.user as any).email).toBe('<EMAIL>');
    });

    it('should preserve other user properties while adding isPrivilegedApp', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user123',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock privileged app authentication
      jest.spyOn(PrivilegedAppAuthStrategy, 'shouldUse').mockReturnValue(true);
      privilegedAppStrategy.authenticate.mockResolvedValue(mockPrivilegedUser);

      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();

      const platformUser = mockRequest.user as any;
      expect(platformUser.token).toBe('user-token');
      expect(platformUser.userType).toBe('ORG_ADMIN');
      expect(platformUser.timezone).toBe('UTC');
      expect(platformUser.sub).toBe('user123');
      expect(platformUser.email).toBe('<EMAIL>');
      expect(platformUser.orgId).toBe('org123');
      expect(platformUser.uid).toBe('user-uid-123');
      expect(platformUser.orgUid).toBe('org-uid-123');
      expect(platformUser.isPrivilegedApp).toBe(true);
    });
  });

  describe('Item 8: Context Improvements', () => {
    it('should handle null trace context gracefully', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock regular API key authentication
      jest.spyOn(PrivilegedAppAuthStrategy, 'shouldUse').mockReturnValue(false);
      apiKeyStrategy.authenticate.mockResolvedValue(mockRegularUser);

      // Mock rTracer.id() to return null
      const rTracer = require('cls-rtracer');
      jest.spyOn(rTracer, 'id').mockReturnValue(null);

      // Should not throw an error even when trace context is null
      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();
    });

    it('should handle trace context without context property gracefully', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock regular API key authentication
      jest.spyOn(PrivilegedAppAuthStrategy, 'shouldUse').mockReturnValue(false);
      apiKeyStrategy.authenticate.mockResolvedValue(mockRegularUser);

      // Mock rTracer.id() to return object without context property
      const rTracer = require('cls-rtracer');
      jest.spyOn(rTracer, 'id').mockReturnValue({
        reqId: 'test-req-id',
        // context property is missing
      });

      // Should not throw an error even when context property is missing
      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();
    });

    it('should safely assign to trace context when available', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock regular API key authentication
      jest.spyOn(PrivilegedAppAuthStrategy, 'shouldUse').mockReturnValue(false);
      apiKeyStrategy.authenticate.mockResolvedValue(mockRegularUser);

      // Mock rTracer.id() to return valid trace object
      const mockTraceContext = {
        existingProperty: 'existing-value',
      };
      const mockTraceId = {
        reqId: 'test-req-id',
        context: mockTraceContext,
      };

      const rTracer = require('cls-rtracer');
      jest.spyOn(rTracer, 'id').mockReturnValue(mockTraceId);

      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();

      // Verify that trace context was safely updated
      expect(mockTraceContext.userId).toBe('user-uid-456');
      expect(mockTraceContext.orgId).toBe('org-uid-456');
      expect(mockTraceContext.existingProperty).toBe('existing-value'); // Existing properties preserved
    });
  });
});
