import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { getRepositoryToken } from '@nestjs/typeorm';
import { FastifyRequest } from 'fastify';
import { AuthenticationGrpcClient, User } from '@repo/nestjs-commons/guards';
import {
  ApiKey,
  AppInstallation,
  User as UserEntity,
} from '@repo/thena-platform-entities';
import { PlatformAuthGuard } from '../guards/platform-auth.guard';
import { PrivilegedAppAuthStrategy } from '../strategies/privileged-app-auth.strategy';
import { PrivilegedAppService } from '../services/privileged-app.service';

describe('Privileged App Authentication - Integration Tests', () => {
  let module: TestingModule;
  let guard: PlatformAuthGuard;
  let strategy: PrivilegedAppAuthStrategy;
  let service: PrivilegedAppService;
  let authClient: jest.Mocked<AuthenticationGrpcClient>;
  let reflector: jest.Mocked<Reflector>;

  const mockBotUser: User = {
    authId: 'auth123',
    sub: 'bot123',
    uid: 'bot-uid-123',
    email: '<EMAIL>',
    userName: 'bot',
    userType: 'BOT',
    scopes: ['read', 'write'],
    orgId: 'org123',
    orgUid: 'org-uid-123',
    orgTier: 'PREMIUM',
    metadata: { userType: 'BOT' },
    token: 'bot-token',
    isPrivilegedApp: true,
  };

  beforeEach(async () => {
    const mockAuthClient = {
      validateKey: jest.fn(),
    };

    const mockReflector = {
      getAllAndOverride: jest.fn(),
    };

    // Mock repositories
    const mockApiKeyRepository = {
      createQueryBuilder: jest.fn().mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn(),
      }),
    };

    const mockAppInstallationRepository = {
      createQueryBuilder: jest.fn().mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn(),
      }),
    };

    const mockUserRepository = {
      createQueryBuilder: jest.fn().mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn(),
      }),
    };

    module = await Test.createTestingModule({
      providers: [
        PlatformAuthGuard,
        PrivilegedAppAuthStrategy,
        PrivilegedAppService,
        {
          provide: AuthenticationGrpcClient,
          useValue: mockAuthClient,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
        {
          provide: getRepositoryToken(ApiKey),
          useValue: mockApiKeyRepository,
        },
        {
          provide: getRepositoryToken(AppInstallation),
          useValue: mockAppInstallationRepository,
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    guard = module.get<PlatformAuthGuard>(PlatformAuthGuard);
    strategy = module.get<PrivilegedAppAuthStrategy>(PrivilegedAppAuthStrategy);
    service = module.get<PrivilegedAppService>(PrivilegedAppService);
    authClient = module.get(AuthenticationGrpcClient);
    reflector = module.get(Reflector);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe('End-to-End Privileged App Authentication Flow', () => {
    it('should complete full authentication flow with all fixes applied', async () => {
      // Setup mock data
      const mockValidationResult = {
        isValid: true,
        user: {
          authId: 'auth456',
          id: 'user-internal-id-123',
          uid: 'user-uid-123',
          email: '<EMAIL>',
          userType: 'ORG_ADMIN',
          organizationId: 'org-internal-id-123',
          organization: {
            uid: 'org-uid-123',
            tier: 'PREMIUM',
          },
          metadata: { userType: 'ORG_ADMIN' },
          timezone: 'UTC',
        },
        metadata: {
          originalBotUserUid: 'bot-uid-123',
          impersonatedUserUid: 'user-uid-123',
          appUid: 'app-uid-123',
          organizationUid: 'org-uid-123',
        },
      };

      // Mock request with mixed-case headers (Item 4)
      const mockRequest = {
        headers: {
          'X-API-Key': 'test-api-key',  // Mixed case
          'X-User-ID': 'user-uid-123',  // Mixed case
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      // Mock that this is not a public route
      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock successful gRPC call (Item 3)
      authClient.validateKey.mockResolvedValue(mockBotUser);

      // Mock successful service validation (Item 7)
      jest.spyOn(service, 'getUserInSameOrganization').mockResolvedValue(mockValidationResult);

      // Mock trace context for safe assignment (Item 8)
      const mockTraceContext = { existingProp: 'value' };
      const rTracer = require('cls-rtracer');
      jest.spyOn(rTracer, 'id').mockReturnValue({
        reqId: 'test-req-id',
        context: mockTraceContext,
      });

      // Execute the full flow
      const result = await guard.canActivate(mockContext);

      // Verify successful authentication
      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();

      const platformUser = mockRequest.user as any;

      // Verify Item 6: Privileged App Flag Propagation
      expect(platformUser.isPrivilegedApp).toBe(true);

      // Verify all user properties are correctly set
      expect(platformUser.sub).toBe('user-internal-id-123');
      expect(platformUser.uid).toBe('user-uid-123');
      expect(platformUser.email).toBe('<EMAIL>');
      expect(platformUser.userType).toBe('ORG_ADMIN');
      expect(platformUser.orgUid).toBe('org-uid-123');

      // Verify Item 8: Safe trace context assignment
      expect(mockTraceContext.userId).toBe('user-uid-123');
      expect(mockTraceContext.orgId).toBe('org-uid-123');
      expect(mockTraceContext.existingProp).toBe('value'); // Preserved

      // Verify Item 4: Case-insensitive headers were handled
      expect(authClient.validateKey).toHaveBeenCalledWith('test-api-key');
      expect(service.getUserInSameOrganization).toHaveBeenCalledWith('test-api-key', 'user-uid-123');
    });

    it('should handle gRPC errors gracefully and return 401', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user-uid-123',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      reflector.getAllAndOverride.mockReturnValue(false);

      // Mock gRPC network error (Item 3)
      const grpcError = new Error('UNAVAILABLE: Connection failed');
      authClient.validateKey.mockRejectedValue(grpcError);

      // Should throw UnauthorizedException, not the original gRPC error
      await expect(guard.canActivate(mockContext)).rejects.toThrow(UnauthorizedException);
      await expect(guard.canActivate(mockContext)).rejects.toThrow('Authentication failed');
    });

    it('should handle null pointer exceptions gracefully', async () => {
      // Mock validation result with missing data (Item 5)
      const mockValidationResultWithNulls = {
        isValid: true,
        user: {
          authId: 'auth456',
          id: 'user-internal-id-123',
          uid: 'user-uid-123',
          email: null, // Missing email
          userType: 'ORG_ADMIN',
          organizationId: 'org-internal-id-123',
          organization: null, // Missing organization
          metadata: { userType: 'ORG_ADMIN' },
          timezone: 'UTC',
        },
        metadata: {
          originalBotUserUid: 'bot-uid-123',
          impersonatedUserUid: 'user-uid-123',
          appUid: 'app-uid-123',
          organizationUid: 'org-uid-123',
        },
      };

      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user-uid-123',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      reflector.getAllAndOverride.mockReturnValue(false);
      authClient.validateKey.mockResolvedValue(mockBotUser);
      jest.spyOn(service, 'getUserInSameOrganization').mockResolvedValue(mockValidationResultWithNulls);

      // Should not throw NPE, should handle gracefully
      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();

      const platformUser = mockRequest.user as any;
      expect(platformUser.email).toBe(''); // Fallback for null email
      expect(platformUser.orgUid).toBe('org-uid-123'); // From metadata
    });

    it('should handle null trace context safely', async () => {
      const mockValidationResult = {
        isValid: true,
        user: {
          authId: 'auth456',
          id: 'user-internal-id-123',
          uid: 'user-uid-123',
          email: '<EMAIL>',
          userType: 'ORG_ADMIN',
          organizationId: 'org-internal-id-123',
          organization: {
            uid: 'org-uid-123',
            tier: 'PREMIUM',
          },
          metadata: { userType: 'ORG_ADMIN' },
          timezone: 'UTC',
        },
        metadata: {
          originalBotUserUid: 'bot-uid-123',
          impersonatedUserUid: 'user-uid-123',
          appUid: 'app-uid-123',
          organizationUid: 'org-uid-123',
        },
      };

      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user-uid-123',
        },
        user: undefined,
      } as FastifyRequest;

      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => ({}),
        getClass: () => ({}),
      } as ExecutionContext;

      reflector.getAllAndOverride.mockReturnValue(false);
      authClient.validateKey.mockResolvedValue(mockBotUser);
      jest.spyOn(service, 'getUserInSameOrganization').mockResolvedValue(mockValidationResult);

      // Mock null trace context (Item 8)
      const rTracer = require('cls-rtracer');
      jest.spyOn(rTracer, 'id').mockReturnValue(null);

      // Should not throw error even with null trace context
      const result = await guard.canActivate(mockContext);

      expect(result).toBe(true);
      expect(mockRequest.user).toBeDefined();
    });
  });
});
