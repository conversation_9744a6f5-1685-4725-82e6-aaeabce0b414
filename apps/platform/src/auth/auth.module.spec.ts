import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
    ApiKey,
    ApiKeyRepository,
    AppInstallation,
    AppInstallationRepository,
    User,
    UserRepository,
} from '@repo/thena-platform-entities';

describe('AuthModule - Repository Dependencies', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        // Mock the repository tokens that should be provided by TypeOrmModule.forFeature
        {
          provide: getRepositoryToken(ApiKey),
          useValue: {
            // Mock repository methods
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(AppInstallation),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        // Mock the custom repositories
        {
          provide: ApiKeyRepository,
          useValue: {
            findOneById: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: AppInstallationRepository,
          useValue: {
            findOneById: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            findOneById: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
          },
        },
      ],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have ApiKeyRepository token available for injection', () => {
    const apiKeyRepository = module.get(getRepositoryToken(ApiKey));
    expect(apiKeyRepository).toBeDefined();
    expect(apiKeyRepository.find).toBeDefined();
  });

  it('should have AppInstallationRepository token available for injection', () => {
    const appInstallationRepository = module.get(getRepositoryToken(AppInstallation));
    expect(appInstallationRepository).toBeDefined();
    expect(appInstallationRepository.find).toBeDefined();
  });

  it('should have UserRepository token available for injection', () => {
    const userRepository = module.get(getRepositoryToken(User));
    expect(userRepository).toBeDefined();
    expect(userRepository.find).toBeDefined();
  });

  it('should have custom ApiKeyRepository available for injection', () => {
    const apiKeyRepository = module.get<ApiKeyRepository>(ApiKeyRepository);
    expect(apiKeyRepository).toBeDefined();
    expect(apiKeyRepository.findOneById).toBeDefined();
  });

  it('should have custom AppInstallationRepository available for injection', () => {
    const appInstallationRepository = module.get<AppInstallationRepository>(AppInstallationRepository);
    expect(appInstallationRepository).toBeDefined();
    expect(appInstallationRepository.findOneById).toBeDefined();
  });

  it('should have custom UserRepository available for injection', () => {
    const userRepository = module.get<UserRepository>(UserRepository);
    expect(userRepository).toBeDefined();
    expect(userRepository.findOneById).toBeDefined();
  });
});
