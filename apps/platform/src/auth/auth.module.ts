import { Modu<PERSON> } from "@nestjs/common";
import { APP_GUARD } from "@nestjs/core";
import { JwtModule } from "@nestjs/jwt";
import { ThrottlerGuard } from "@nestjs/throttler";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
    ApiKeyAuthStrategy,
    ApiKeyGrpcStrategy,
    AUTH_GRPC_SERVICE_URL_TOKEN,
    AuthenticationGrpcClient,
    BearerTokenGrpcStrategy,
    BearerTokenHttpStrategy,
    UserOrgInternalGrpcStrategy,
} from "@repo/nestjs-commons/guards";
import { OrganizationTierGuard } from "@repo/nestjs-commons/guards/organization-tier/index";
import {
    ApiKey,
    ApiKeyRepository,
    AppInstallation,
    AppInstallationRepository,
    User,
    UserRepository,
} from "@repo/thena-platform-entities";
import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { OrganizationModule } from "../organization/organization.module";
import { UsersModule } from "../users/users.module";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { PlatformAuthGuard } from "./guards/platform-auth.guard";
import { PrivilegedAppService } from "./services/privileged-app.service";
import { PrivilegedAppAuthStrategy } from "./strategies/privileged-app-auth.strategy";

@Module({
  imports: [
    UsersModule,
    OrganizationModule,
    ConfigModule,
    CommonModule,
    TypeOrmModule.forFeature([
      ApiKey,
      ApiKeyRepository,
      AppInstallation,
      AppInstallationRepository,
      User,
      UserRepository,
    ]),
    JwtModule.registerAsync({
      global: true,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get(ConfigKeys.JWT_SECRET),
        signOptions: { expiresIn: "12h" },
      }),
    }),
  ],
  controllers: [AuthController],
  providers: [
    ApiKeyAuthStrategy,
    BearerTokenHttpStrategy,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
    AuthService,
    PrivilegedAppService,
    PrivilegedAppAuthStrategy,
    PlatformAuthGuard,
    {
      provide: AUTH_GRPC_SERVICE_URL_TOKEN,
      useFactory: (configService: ConfigService) =>
        configService.get(ConfigKeys.AUTH_GRPC_URL),
      inject: [ConfigService],
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      // Globally apply the platform auth guard to all routes
      provide: APP_GUARD,
      useClass: PlatformAuthGuard,
    },

    {
      provide: APP_GUARD,
      useClass: OrganizationTierGuard,
    },

    // Authentication Grpc Client
    AuthenticationGrpcClient,
  ],
  exports: [
    AuthService,
    ApiKeyAuthStrategy,
    BearerTokenHttpStrategy,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
  ],
})
export class AuthModule {}
