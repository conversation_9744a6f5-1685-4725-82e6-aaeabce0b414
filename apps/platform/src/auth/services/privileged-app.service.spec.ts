import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  ApiKey,
  AppInstallation,
  User,
} from '@repo/thena-platform-entities';
import { PrivilegedAppService, PrivilegedAppValidationResult } from './privileged-app.service';

describe('PrivilegedAppService - Item 7: Inconsistent Identifier Usage', () => {
  let service: PrivilegedAppService;
  let apiKeyRepository: jest.Mocked<Repository<ApiKey>>;
  let appInstallationRepository: jest.Mocked<Repository<AppInstallation>>;
  let userRepository: jest.Mocked<Repository<User>>;

  const mockBotUser = {
    id: 'bot-internal-id-123',
    uid: 'bot-uid-123',
    email: '<EMAIL>',
    organizationId: 'org-internal-id-123',
    organization: {
      id: 'org-internal-id-123',
      uid: 'org-uid-123',
    },
  };

  const mockTargetUser = {
    id: 'user-internal-id-456',
    uid: 'user-uid-456',
    email: '<EMAIL>',
    organizationId: 'org-internal-id-123',
    organization: {
      id: 'org-internal-id-123',
      uid: 'org-uid-123',
    },
  };

  const mockApiKey = {
    id: 'apikey-internal-id-789',
    keyId: 'test-key-id',
    user: mockBotUser,
    isActive: true,
  };

  const mockApp = {
    id: 'app-internal-id-999',
    uid: 'app-uid-999',
    isThenaPrivileged: true,
  };

  const mockAppInstallation = {
    id: 'installation-internal-id-888',
    botUserId: 'bot-uid-123',
    status: 'active',
    app: mockApp,
  };

  beforeEach(async () => {
    const mockApiKeyRepository = {
      createQueryBuilder: jest.fn(),
    };

    const mockAppInstallationRepository = {
      createQueryBuilder: jest.fn(),
    };

    const mockUserRepository = {
      createQueryBuilder: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PrivilegedAppService,
        {
          provide: getRepositoryToken(ApiKey),
          useValue: mockApiKeyRepository,
        },
        {
          provide: getRepositoryToken(AppInstallation),
          useValue: mockAppInstallationRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<PrivilegedAppService>(PrivilegedAppService);
    apiKeyRepository = module.get(getRepositoryToken(ApiKey));
    appInstallationRepository = module.get(getRepositoryToken(AppInstallation));
    userRepository = module.get(getRepositoryToken(User));
  });

  describe('Item 7: Inconsistent Identifier Usage', () => {
    it('should return UIDs instead of internal IDs in metadata', async () => {
      // Mock the query builder chain for API key lookup
      const apiKeyQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockApiKey),
      };
      apiKeyRepository.createQueryBuilder.mockReturnValue(apiKeyQueryBuilder);

      // Mock the query builder chain for user lookup
      const userQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockTargetUser),
      };
      userRepository.createQueryBuilder.mockReturnValue(userQueryBuilder);

      // Mock the query builder chain for app installation lookup
      const appInstallationQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockAppInstallation),
      };
      appInstallationRepository.createQueryBuilder.mockReturnValue(appInstallationQueryBuilder);

      // Mock isPrivilegedApp to return true
      jest.spyOn(service, 'isPrivilegedApp').mockResolvedValue(true);

      const result: PrivilegedAppValidationResult = await service.getUserInSameOrganization(
        'test-key-id.test-secret',
        'user-uid-456'
      );

      expect(result.isValid).toBe(true);
      expect(result.metadata).toBeDefined();
      
      // Verify that UIDs are used instead of internal IDs
      expect(result.metadata!.originalBotUserUid).toBe('bot-uid-123');
      expect(result.metadata!.impersonatedUserUid).toBe('user-uid-456');
      expect(result.metadata!.appUid).toBe('app-uid-999');
      expect(result.metadata!.organizationUid).toBe('org-uid-123');

      // Verify that internal IDs are NOT used
      expect(result.metadata!.originalBotUserUid).not.toBe('bot-internal-id-123');
      expect(result.metadata!.impersonatedUserUid).not.toBe('user-internal-id-456');
      expect(result.metadata!.appUid).not.toBe('app-internal-id-999');
      expect(result.metadata!.organizationUid).not.toBe('org-internal-id-123');
    });

    it('should handle missing organization gracefully in metadata', async () => {
      const mockTargetUserWithoutOrg = {
        ...mockTargetUser,
        organization: null,
      };

      // Mock the query builder chain for API key lookup
      const apiKeyQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockApiKey),
      };
      apiKeyRepository.createQueryBuilder.mockReturnValue(apiKeyQueryBuilder);

      // Mock the query builder chain for user lookup
      const userQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockTargetUserWithoutOrg),
      };
      userRepository.createQueryBuilder.mockReturnValue(userQueryBuilder);

      // Mock the query builder chain for app installation lookup
      const appInstallationQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockAppInstallation),
      };
      appInstallationRepository.createQueryBuilder.mockReturnValue(appInstallationQueryBuilder);

      // Mock isPrivilegedApp to return true
      jest.spyOn(service, 'isPrivilegedApp').mockResolvedValue(true);

      const result: PrivilegedAppValidationResult = await service.getUserInSameOrganization(
        'test-key-id.test-secret',
        'user-uid-456'
      );

      expect(result.isValid).toBe(true);
      expect(result.metadata).toBeDefined();
      
      // Verify that organizationUid defaults to empty string when organization is null
      expect(result.metadata!.organizationUid).toBe('');
      
      // Verify other UIDs are still correct
      expect(result.metadata!.originalBotUserUid).toBe('bot-uid-123');
      expect(result.metadata!.impersonatedUserUid).toBe('user-uid-456');
      expect(result.metadata!.appUid).toBe('app-uid-999');
    });

    it('should use consistent UID naming convention', () => {
      // This test verifies that the interface uses the correct naming convention
      const mockResult: PrivilegedAppValidationResult = {
        isValid: true,
        metadata: {
          originalBotUserUid: 'bot-uid-123',
          impersonatedUserUid: 'user-uid-456',
          appUid: 'app-uid-999',
          organizationUid: 'org-uid-123',
        },
      };

      // Verify the property names follow the UID convention
      expect(mockResult.metadata).toHaveProperty('originalBotUserUid');
      expect(mockResult.metadata).toHaveProperty('impersonatedUserUid');
      expect(mockResult.metadata).toHaveProperty('appUid');
      expect(mockResult.metadata).toHaveProperty('organizationUid');

      // Verify the old ID-based property names are not present
      expect(mockResult.metadata).not.toHaveProperty('originalBotUserId');
      expect(mockResult.metadata).not.toHaveProperty('impersonatedUserId');
      expect(mockResult.metadata).not.toHaveProperty('appId');
      expect(mockResult.metadata).not.toHaveProperty('organizationId');
    });
  });
});
