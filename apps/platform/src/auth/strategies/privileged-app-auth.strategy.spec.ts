import { Test, TestingModule } from '@nestjs/testing';
import { UnauthorizedException } from '@nestjs/common';
import { AuthenticationGrpcClient, User } from '@repo/nestjs-commons/guards';
import { FastifyRequest } from 'fastify';
import { PrivilegedAppAuthStrategy } from './privileged-app-auth.strategy';
import { PrivilegedAppService } from '../services/privileged-app.service';

describe('PrivilegedAppAuthStrategy - Error Handling & Security Improvements', () => {
  let strategy: PrivilegedAppAuthStrategy;
  let authClient: jest.Mocked<AuthenticationGrpcClient>;
  let privilegedAppService: jest.Mocked<PrivilegedAppService>;

  const mockBotUser: User = {
    authId: 'auth123',
    sub: 'bot123',
    uid: 'bot-uid-123',
    email: '<EMAIL>',
    userName: 'bot',
    userType: 'BOT',
    scopes: ['read', 'write'],
    orgId: 'org123',
    orgUid: 'org-uid-123',
    orgTier: 'PREMIUM',
    metadata: { userType: 'BOT' },
    token: 'bot-token',
  };

  const mockValidationResult = {
    isValid: true,
    user: {
      authId: 'auth456',
      id: 'user123',
      uid: 'user-uid-123',
      email: '<EMAIL>',
      userType: 'ORG_ADMIN',
      organizationId: 'org123',
      organization: {
        uid: 'org-uid-123',
        tier: 'PREMIUM',
      },
      metadata: { userType: 'ORG_ADMIN' },
      timezone: 'UTC',
    },
    metadata: {
      originalBotUserId: 'bot123',
      impersonatedUserId: 'user123',
      appId: 'app123',
      organizationId: 'org123',
    },
  };

  beforeEach(async () => {
    const mockAuthClient = {
      validateKey: jest.fn(),
    };

    const mockPrivilegedAppService = {
      getUserInSameOrganization: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PrivilegedAppAuthStrategy,
        {
          provide: AuthenticationGrpcClient,
          useValue: mockAuthClient,
        },
        {
          provide: PrivilegedAppService,
          useValue: mockPrivilegedAppService,
        },
      ],
    }).compile();

    strategy = module.get<PrivilegedAppAuthStrategy>(PrivilegedAppAuthStrategy);
    authClient = module.get(AuthenticationGrpcClient);
    privilegedAppService = module.get(PrivilegedAppService);
  });

  describe('Item 3: gRPC Error Handling', () => {
    it('should handle gRPC network errors and return 401 instead of 500', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user123',
        },
      } as FastifyRequest;

      // Mock gRPC network error
      const grpcError = new Error('UNAVAILABLE: Connection failed');
      authClient.validateKey.mockRejectedValue(grpcError);

      await expect(strategy.authenticate(mockRequest)).rejects.toThrow(
        new UnauthorizedException('Authentication failed')
      );

      expect(authClient.validateKey).toHaveBeenCalledWith('test-api-key');
    });

    it('should log gRPC errors appropriately', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key-12345',
          'x-user-id': 'user123',
        },
      } as FastifyRequest;

      const grpcError = new Error('DEADLINE_EXCEEDED: Request timeout');
      authClient.validateKey.mockRejectedValue(grpcError);

      const loggerSpy = jest.spyOn(strategy['logger'], 'error');

      await expect(strategy.authenticate(mockRequest)).rejects.toThrow(UnauthorizedException);

      expect(loggerSpy).toHaveBeenCalledWith('gRPC error during API key validation', {
        error: 'DEADLINE_EXCEEDED: Request timeout',
        apiKeyPrefix: 'test-api...',
        stack: expect.any(String),
      });
    });
  });

  describe('Item 4: Header Case Sensitivity', () => {
    it('should handle mixed-case X-API-Key header', async () => {
      const mockRequest = {
        headers: {
          'X-API-Key': 'test-api-key',
          'x-user-id': 'user123',
        },
      } as FastifyRequest;

      authClient.validateKey.mockResolvedValue(mockBotUser);
      privilegedAppService.getUserInSameOrganization.mockResolvedValue(mockValidationResult);

      const result = await strategy.authenticate(mockRequest);

      expect(result).toBeDefined();
      expect(authClient.validateKey).toHaveBeenCalledWith('test-api-key');
    });

    it('should handle mixed-case X-User-ID header', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'X-User-ID': 'user123',
        },
      } as FastifyRequest;

      authClient.validateKey.mockResolvedValue(mockBotUser);
      privilegedAppService.getUserInSameOrganization.mockResolvedValue(mockValidationResult);

      const result = await strategy.authenticate(mockRequest);

      expect(result).toBeDefined();
      expect(privilegedAppService.getUserInSameOrganization).toHaveBeenCalledWith('test-api-key', 'user123');
    });

    it('shouldUse should handle mixed-case headers', () => {
      const mockRequest1 = {
        headers: {
          'X-API-Key': 'test-key',
          'X-User-ID': 'user123',
        },
      } as FastifyRequest;

      const mockRequest2 = {
        headers: {
          'x-api-key': 'test-key',
          'X-User-Id': 'user123',
        },
      } as FastifyRequest;

      expect(PrivilegedAppAuthStrategy.shouldUse(mockRequest1)).toBe(true);
      expect(PrivilegedAppAuthStrategy.shouldUse(mockRequest2)).toBe(true);
    });
  });

  describe('Item 5: Null Pointer Exception Prevention', () => {
    it('should handle missing email gracefully', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user123',
        },
      } as FastifyRequest;

      const validationResultWithoutEmail = {
        ...mockValidationResult,
        user: {
          ...mockValidationResult.user,
          email: null,
        },
      };

      authClient.validateKey.mockResolvedValue(mockBotUser);
      privilegedAppService.getUserInSameOrganization.mockResolvedValue(validationResultWithoutEmail);

      const loggerSpy = jest.spyOn(strategy['logger'], 'warn');

      const result = await strategy.authenticate(mockRequest);

      expect(result.userName).toBe('unknown');
      expect(result.email).toBe('');
      expect(loggerSpy).toHaveBeenCalledWith('Email missing during user impersonation', {
        userId: 'user123',
      });
    });

    it('should handle invalid email format gracefully', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user123',
        },
      } as FastifyRequest;

      const validationResultWithInvalidEmail = {
        ...mockValidationResult,
        user: {
          ...mockValidationResult.user,
          email: 'invalid-email-format',
        },
      };

      authClient.validateKey.mockResolvedValue(mockBotUser);
      privilegedAppService.getUserInSameOrganization.mockResolvedValue(validationResultWithInvalidEmail);

      const result = await strategy.authenticate(mockRequest);

      expect(result.userName).toBe('invalid-email-format');
      expect(result.email).toBe('invalid-email-format');
    });

    it('should handle missing organization data gracefully', async () => {
      const mockRequest = {
        headers: {
          'x-api-key': 'test-api-key',
          'x-user-id': 'user123',
        },
      } as FastifyRequest;

      const validationResultWithoutOrg = {
        ...mockValidationResult,
        user: {
          ...mockValidationResult.user,
          organization: null,
        },
      };

      authClient.validateKey.mockResolvedValue(mockBotUser);
      privilegedAppService.getUserInSameOrganization.mockResolvedValue(validationResultWithoutOrg);

      const loggerSpy = jest.spyOn(strategy['logger'], 'warn');

      const result = await strategy.authenticate(mockRequest);

      expect(result.orgUid).toBe('');
      expect(result.orgTier).toBe('');
      expect(loggerSpy).toHaveBeenCalledWith('Organization data missing during user impersonation', {
        userId: 'user123',
        organizationId: 'org123',
      });
    });
  });
});
