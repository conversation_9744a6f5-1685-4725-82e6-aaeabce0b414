import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  ApiKey,
  ApiKeyRepository,
  App,
  AppInstallation,
  AppInstallationRepository,
  AppRepository,
  CachedApiKeyRepository,
  CachedAppInstallationsRepository,
  CachedOrganizationRepository,
  CachedUserRepository,
  OAuthAuthorizationCode,
  OAuthClient,
  OAuthToken,
  Organization,
  OrganizationRepository,
  TransactionService,
  User,
  UserRepository,
} from '@repo/thena-platform-entities';

describe('AuthenticationModule - Repository Configuration', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        // Mock the repository tokens that should be provided by TypeOrmModule.forFeature
        {
          provide: getRepositoryToken(User),
          useValue: { find: jest.fn() },
        },
        {
          provide: getRepositoryToken(Organization),
          useValue: { find: jest.fn() },
        },
        {
          provide: getRepositoryToken(ApiKey),
          useValue: { find: jest.fn() },
        },
        {
          provide: getRepositoryToken(App),
          useValue: { find: jest.fn() },
        },
        {
          provide: getRepositoryToken(AppInstallation),
          useValue: { find: jest.fn() },
        },
        {
          provide: getRepositoryToken(OAuthClient),
          useValue: { find: jest.fn() },
        },
        {
          provide: getRepositoryToken(OAuthAuthorizationCode),
          useValue: { find: jest.fn() },
        },
        {
          provide: getRepositoryToken(OAuthToken),
          useValue: { find: jest.fn() },
        },
        // Mock the base repositories (these should NOT be in providers array)
        {
          provide: UserRepository,
          useValue: { findOneById: jest.fn() },
        },
        {
          provide: OrganizationRepository,
          useValue: { findOneById: jest.fn() },
        },
        {
          provide: ApiKeyRepository,
          useValue: { findOneById: jest.fn() },
        },
        {
          provide: AppRepository,
          useValue: { findOneById: jest.fn() },
        },
        {
          provide: AppInstallationRepository,
          useValue: { findOneById: jest.fn() },
        },
        // Mock the cached repositories (these SHOULD be in providers array)
        {
          provide: CachedUserRepository,
          useValue: { findOneById: jest.fn() },
        },
        {
          provide: CachedOrganizationRepository,
          useValue: { findOneById: jest.fn() },
        },
        {
          provide: CachedApiKeyRepository,
          useValue: { findOneById: jest.fn() },
        },
        {
          provide: CachedAppInstallationsRepository,
          useValue: { findOneById: jest.fn() },
        },
        // Mock other services
        {
          provide: TransactionService,
          useValue: {},
        },
      ],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have base repositories available for injection (from TypeOrmModule.forFeature)', () => {
    const userRepository = module.get<UserRepository>(UserRepository);
    const organizationRepository = module.get<OrganizationRepository>(OrganizationRepository);
    const apiKeyRepository = module.get<ApiKeyRepository>(ApiKeyRepository);
    const appRepository = module.get<AppRepository>(AppRepository);
    const appInstallationRepository = module.get<AppInstallationRepository>(AppInstallationRepository);
    
    expect(userRepository).toBeDefined();
    expect(organizationRepository).toBeDefined();
    expect(apiKeyRepository).toBeDefined();
    expect(appRepository).toBeDefined();
    expect(appInstallationRepository).toBeDefined();
  });

  it('should have cached repositories available for injection (from providers array)', () => {
    const cachedUserRepository = module.get<CachedUserRepository>(CachedUserRepository);
    const cachedOrganizationRepository = module.get<CachedOrganizationRepository>(CachedOrganizationRepository);
    const cachedApiKeyRepository = module.get<CachedApiKeyRepository>(CachedApiKeyRepository);
    const cachedAppInstallationsRepository = module.get<CachedAppInstallationsRepository>(CachedAppInstallationsRepository);
    
    expect(cachedUserRepository).toBeDefined();
    expect(cachedOrganizationRepository).toBeDefined();
    expect(cachedApiKeyRepository).toBeDefined();
    expect(cachedAppInstallationsRepository).toBeDefined();
  });

  it('should have entity repository tokens available for injection', () => {
    const userEntityRepository = module.get(getRepositoryToken(User));
    const organizationEntityRepository = module.get(getRepositoryToken(Organization));
    const apiKeyEntityRepository = module.get(getRepositoryToken(ApiKey));
    const appEntityRepository = module.get(getRepositoryToken(App));
    const appInstallationEntityRepository = module.get(getRepositoryToken(AppInstallation));
    
    expect(userEntityRepository).toBeDefined();
    expect(organizationEntityRepository).toBeDefined();
    expect(apiKeyEntityRepository).toBeDefined();
    expect(appEntityRepository).toBeDefined();
    expect(appInstallationEntityRepository).toBeDefined();
  });

  it('should have TransactionService available for injection', () => {
    const transactionService = module.get<TransactionService>(TransactionService);
    expect(transactionService).toBeDefined();
  });
});
